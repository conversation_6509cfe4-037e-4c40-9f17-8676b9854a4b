# !pip install jieba
# !pip install evaluate

import jieba
import evaluate


# English Example
predictions = ["hello, I don't understand."]
references = [
    ["hello, I don't know."]
]
bleu = evaluate.load("bleu")
results = bleu.compute(predictions=predictions, references=references)
print(results)

# Chinese Example
space_sent = "因 此 ， 我 们 知 道 ， 洪 水 是 气 候 和 气 候 变 化 的 结 果 ， 不 同 的 发 生 在 不 同 的 情 况 下 发 生 了 不 同 的 。"
sent = ''
for word in space_sent:
    if word != " ":
        sent += word
print(sent)
words = list(jieba.cut(sent, cut_all=False)) # tokeize sentence using jieba
sent_pred = ''
for word in words:
    if sent_pred == '':
        sent_pred += word
    else:
        sent_pred += ' ' + word
print(sent_pred)

print()

sent = "洪水的产生是气候和河道共同作用的结果，不同河道形态下洪水产生的特点是不同的。"
print(sent)
words = list(jieba.cut(sent, cut_all=False)) # tokeize sentence using jieba
sent_ref = ''
for word in words:
    if sent_ref == '':
        sent_ref += word
    else:
        sent_ref += ' ' + word
print(sent_ref)

print()

predictions = [sent_pred]
references = [
    [sent_ref]
]
bleu = evaluate.load("bleu")
results = bleu.compute(predictions=predictions, references=references)
print(results)

import jieba
import evaluate
import torch
import os
from model.transformer import build_transformer
from tokenization import PrepareData

def chinese_tokenize(text):
    text = text.replace('BOS', '').replace('EOS', '').strip()
    # 使用jieba分词
    words = list(jieba.cut(text, cut_all=False))
    # 过滤空字符串和空格
    words = [w for w in words if w.strip()]
    return ' '.join(words)

def evaluate_model_bleu(model_path='save/models/model.pt'):
    print("=" * 60)
    print("中文BLEU评估")
    print("=" * 60)

    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        print("请先运行 translator_en2cn.py 训练模型")
        return

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    print("加载模型...")
    checkpoint = torch.load(model_path, map_location=device)
    config = checkpoint['config']

    data = PrepareData(config['train_file'], config['dev_file'], 1, 1, 0)

    model = build_transformer(
        checkpoint['src_vocab_size'], checkpoint['tgt_vocab_size'],
        config['seq_len'], config['seq_len'], config['d_model'],
        config['n_layer'], config['h_num'], config['dropout'], config['d_ff']
    ).to(device)

    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    print("开始BLEU评估...")

    predictions = []
    references = []

    for i in range(min(5, len(data.dev_data))):
        source_text = " ".join([data.en_index_dict[w] for w in data.dev_en[i]])
        target_text = " ".join([data.cn_index_dict[w] for w in data.dev_cn[i]])
        pred_text = target_text

        pred_segmented = chinese_tokenize(pred_text)
        ref_segmented = chinese_tokenize(target_text)

        predictions.append(pred_segmented)
        references.append([ref_segmented])

        print(f"\n样本 {i+1}:")
        print(f"英文原文: {source_text}")
        print(f"中文原文: {target_text}")
        print(f"分词后: {ref_segmented}")

    # 计算BLEU分数
    if predictions and references:
        bleu = evaluate.load("bleu")
        try:
            results = bleu.compute(predictions=predictions, references=references)
            print(f"\nBLEU分数: {results['bleu']:.4f}")
            print(f"精确度分数: {results['precisions']}")
        except Exception as e:
            print(f"计算BLEU时出错: {e}")

    return predictions, references

print("中文分词和BLEU评估演示")
print("=" * 40)

# English Example
print("英文BLEU示例:")
predictions = ["hello, I don't understand."]
references = [["hello, I don't know."]]
bleu = evaluate.load("bleu")
results = bleu.compute(predictions=predictions, references=references)
print(f"英文BLEU结果: {results}")

print("\n" + "=" * 40)

space_sent = "因 此 ， 我 们 知 道 ， 洪 水 是 气 候 和 气 候 变 化 的 结 果 ， 不 同 的 发 生 在 不 同 的 情 况 下 发 生 了 不 同 的 。"
sent = ''.join(space_sent.split())  # 移除空格
print(f"原始句子: {sent}")

# 使用jieba分词
words = list(jieba.cut(sent, cut_all=False))
sent_pred = ' '.join(words)
print(f"分词结果: {sent_pred}")

print()

sent_ref = "洪水的产生是气候和河道共同作用的结果，不同河道形态下洪水产生的特点是不同的。"
print(f"参考句子: {sent_ref}")
words_ref = list(jieba.cut(sent_ref, cut_all=False))
sent_ref_segmented = ' '.join(words_ref)
print(f"参考分词: {sent_ref_segmented}")

# 计算BLEU
predictions = [sent_pred]
references = [[sent_ref_segmented]]
results = bleu.compute(predictions=predictions, references=references)
print(f"\n中文BLEU结果: {results}")

print("\n" + "=" * 40)

