import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from collections import Counter
from nltk.tokenize import word_tokenize
import nltk
import os
import requests
from sklearn.model_selection import train_test_split

try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')


class SentimentDataset(Dataset):
    def __init__(self, texts, labels, word_to_id, max_length=128, pad_id=0):
        self.texts = texts
        self.labels = labels
        self.word_to_id = word_to_id
        self.max_length = max_length
        self.pad_id = pad_id

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = self.texts[idx]
        label = self.labels[idx]

        tokens = word_tokenize(text.lower())
        token_ids = [self.word_to_id.get(token, self.word_to_id.get('<UNK>', 1)) for token in tokens]

        if len(token_ids) > self.max_length:
            token_ids = token_ids[:self.max_length]

        padding_length = self.max_length - len(token_ids)
        token_ids.extend([self.pad_id] * padding_length)

        mask = [1] * min(len(tokens), self.max_length) + [0] * padding_length

        return {
            'input_ids': torch.tensor(token_ids, dtype=torch.long),
            'attention_mask': torch.tensor(mask, dtype=torch.bool),
            'labels': torch.tensor(label, dtype=torch.long)
        }


def download_sentiment_data():
    print("Downloading sentiment dataset...")

    data_dir = 'data/sentiment'
    os.makedirs(data_dir, exist_ok=True)

    try:
        from datasets import load_dataset
        dataset = load_dataset("mteb/tweet_sentiment_extraction")

        train_data = []
        for item in dataset['train']:
            if 'text' in item and 'label' in item:
                train_data.append({
                    'text': item['text'],
                    'label': item['label']
                })

        test_data = []
        for item in dataset['test']:
            if 'text' in item and 'label' in item:
                test_data.append({
                    'text': item['text'],
                    'label': item['label']
                })

        train_df = pd.DataFrame(train_data)
        test_df = pd.DataFrame(test_data)

    except Exception as e:
        print(f"Could not load from HuggingFace datasets: {e}")
        print("Creating synthetic sentiment data for demonstration...")

        positive_texts = [
            "I love this movie! It's absolutely fantastic.",
            "This is the best day ever! So happy and excited.",
            "Amazing product, highly recommend it to everyone.",
            "Wonderful experience, will definitely come back.",
            "Great service and friendly staff, loved it!",
            "Perfect weather today, feeling so good!",
            "Excellent quality and fast delivery, very satisfied.",
            "Beautiful scenery and peaceful atmosphere.",
            "Outstanding performance, truly impressive work.",
            "Delicious food and cozy restaurant ambiance."
        ] * 100

        negative_texts = [
            "This is terrible, worst experience ever.",
            "Completely disappointed with the service.",
            "Poor quality product, waste of money.",
            "Horrible weather ruined my entire day.",
            "Awful customer service, very rude staff.",
            "Disgusting food, will never eat here again.",
            "Broken product arrived, very frustrated.",
            "Boring movie, fell asleep halfway through.",
            "Expensive and low quality, not worth it.",
            "Uncomfortable seats and noisy environment."
        ] * 100

        neutral_texts = [
            "The product is okay, nothing special.",
            "Average service, neither good nor bad.",
            "It's an ordinary day, nothing exciting.",
            "The movie was fine, not great but watchable.",
            "Standard quality, meets basic expectations.",
            "Normal weather conditions for this season.",
            "Regular customer service, no complaints.",
            "The food was decent, could be better.",
            "Typical experience, nothing to complain about.",
            "Fair price for what you get, reasonable."
        ] * 100

        texts = positive_texts + negative_texts + neutral_texts
        labels = [2] * len(positive_texts) + [0] * len(negative_texts) + [1] * len(neutral_texts)

        data = list(zip(texts, labels))
        np.random.shuffle(data)
        texts, labels = zip(*data)

        train_texts, test_texts, train_labels, test_labels = train_test_split(
            texts, labels, test_size=0.2, random_state=42, stratify=labels
        )

        train_df = pd.DataFrame({'text': train_texts, 'label': train_labels})
        test_df = pd.DataFrame({'text': test_texts, 'label': test_labels})

    train_df.to_csv(os.path.join(data_dir, 'train.csv'), index=False)
    test_df.to_csv(os.path.join(data_dir, 'test.csv'), index=False)

    print(f"Dataset saved to {data_dir}")
    print(f"Train samples: {len(train_df)}")
    print(f"Test samples: {len(test_df)}")
    print(f"Label distribution in train: {train_df['label'].value_counts().to_dict()}")

    return train_df, test_df


def build_vocabulary(texts, min_freq=2, max_vocab_size=10000):
    print("Building vocabulary...")

    all_tokens = []
    for text in texts:
        if isinstance(text, str):
            tokens = word_tokenize(text.lower())
            all_tokens.extend(tokens)

    token_counts = Counter(all_tokens)

    vocab = ['<PAD>', '<UNK>']
    for token, count in token_counts.most_common(max_vocab_size - 2):
        if count >= min_freq:
            vocab.append(token)

    word_to_id = {word: idx for idx, word in enumerate(vocab)}
    id_to_word = {idx: word for word, idx in word_to_id.items()}

    print(f"Vocabulary size: {len(vocab)}")
    print(f"Most common tokens: {list(token_counts.most_common(10))}")

    return word_to_id, id_to_word


def prepare_sentiment_data(batch_size=32, max_length=128, min_freq=2, max_vocab_size=10000):
    data_dir = 'data/sentiment'

    if not os.path.exists(os.path.join(data_dir, 'train.csv')):
        train_df, test_df = download_sentiment_data()
    else:
        train_df = pd.read_csv(os.path.join(data_dir, 'train.csv'))
        test_df = pd.read_csv(os.path.join(data_dir, 'test.csv'))

    train_df = train_df.dropna()
    test_df = test_df.dropna()

    train_texts = train_df['text'].tolist()
    train_labels = train_df['label'].tolist()
    test_texts = test_df['text'].tolist()
    test_labels = test_df['label'].tolist()

    word_to_id, id_to_word = build_vocabulary(train_texts, min_freq, max_vocab_size)

    train_dataset = SentimentDataset(train_texts, train_labels, word_to_id, max_length)
    test_dataset = SentimentDataset(test_texts, test_labels, word_to_id, max_length)

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    vocab_info = {
        'word_to_id': word_to_id,
        'id_to_word': id_to_word,
        'vocab_size': len(word_to_id),
        'num_classes': len(set(train_labels)),
        'label_names': {0: 'negative', 1: 'neutral', 2: 'positive'}
    }

    return train_loader, test_loader, vocab_info


if __name__ == "__main__":
    train_loader, test_loader, vocab_info = prepare_sentiment_data()
    print(f"Vocabulary size: {vocab_info['vocab_size']}")
    print(f"Number of classes: {vocab_info['num_classes']}")
    print(f"Label names: {vocab_info['label_names']}")

    for batch in train_loader:
        print(f"Batch input shape: {batch['input_ids'].shape}")
        print(f"Batch mask shape: {batch['attention_mask'].shape}")
        print(f"Batch labels shape: {batch['labels'].shape}")
        break
