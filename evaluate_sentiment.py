#!/usr/bin/env python
# coding: utf-8

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import seaborn as sns
import os
from nltk.tokenize import word_tokenize

from model.sentiment_transformer import build_sentiment_transformer
from sentiment_data import prepare_sentiment_data

def predict_sentiment(model, text, word_to_id, label_names, max_length=128, device='cpu'):
    model.eval()
    
    tokens = word_tokenize(text.lower())
    token_ids = [word_to_id.get(token, word_to_id.get('<UNK>', 1)) for token in tokens]
    
    if len(token_ids) > max_length:
        token_ids = token_ids[:max_length]
    
    padding_length = max_length - len(token_ids)
    token_ids.extend([0] * padding_length)
    
    mask = [1] * min(len(tokens), max_length) + [0] * padding_length
    
    input_ids = torch.tensor([token_ids], dtype=torch.long).to(device)
    attention_mask = torch.tensor([mask], dtype=torch.bool).to(device)
    
    with torch.no_grad():
        outputs = model(input_ids, attention_mask)
        probabilities = torch.softmax(outputs, dim=-1)
        predicted_class = torch.argmax(outputs, dim=-1).item()
        confidence = probabilities[0][predicted_class].item()
    
    return {
        'predicted_label': label_names[predicted_class],
        'confidence': confidence,
        'probabilities': {label_names[i]: prob.item() for i, prob in enumerate(probabilities[0])}
    }

def evaluate_model_comprehensive(model_path='save/sentiment_models/best_sentiment_model.pt'):
    print("=" * 60)
    print("Sentiment Analysis Model Evaluation")
    print("=" * 60)
    
    if not os.path.exists(model_path):
        print(f"Model file not found: {model_path}")
        print("Please run train_sentiment.py first to train the model")
        return
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    vocab_path = 'save/sentiment_models/vocab.pt'
    if not os.path.exists(vocab_path):
        print(f"Vocabulary file not found: {vocab_path}")
        return
    
    print("Loading vocabulary and data...")
    vocab_info = torch.load(vocab_path, map_location='cpu')
    
    _, test_loader, _ = prepare_sentiment_data(
        batch_size=32,
        max_length=128,
        min_freq=vocab_info.get('min_freq', 2),
        max_vocab_size=vocab_info['vocab_size']
    )
    
    print("Building model...")
    model = build_sentiment_transformer(
        vocab_size=vocab_info['vocab_size'],
        seq_len=128,
        num_classes=vocab_info['num_classes'],
        d_model=512,
        N=6,
        h=8,
        dropout=0.1,
        d_ff=2048
    ).to(device)
    
    print("Loading trained model...")
    model.load_state_dict(torch.load(model_path, map_location=device))
    
    print("Evaluating on test set...")
    model.eval()
    all_predictions = []
    all_labels = []
    total_loss = 0
    criterion = nn.CrossEntropyLoss()
    
    with torch.no_grad():
        for batch in test_loader:
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            outputs = model(input_ids, attention_mask)
            loss = criterion(outputs, labels)
            total_loss += loss.item()
            
            _, predicted = torch.max(outputs.data, 1)
            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    avg_loss = total_loss / len(test_loader)
    accuracy = accuracy_score(all_labels, all_predictions)
    
    print(f"\nTest Results:")
    print(f"Average Loss: {avg_loss:.4f}")
    print(f"Accuracy: {accuracy:.4f}")
    
    class_names = list(vocab_info['label_names'].values())
    print(f"\nDetailed Classification Report:")
    print(classification_report(all_labels, all_predictions, target_names=class_names))
    
    cm = confusion_matrix(all_labels, all_predictions)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Confusion Matrix - Test Set')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.tight_layout()
    plt.savefig('save/sentiment_models/test_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("\nTesting with sample texts:")
    sample_texts = [
        "I love this product! It's absolutely amazing and works perfectly.",
        "This is terrible. Worst purchase I've ever made. Complete waste of money.",
        "The product is okay. Nothing special but it does what it's supposed to do.",
        "Fantastic service! The staff was very helpful and friendly.",
        "Poor quality and overpriced. Would not recommend to anyone.",
        "Average experience. Not bad but not great either."
    ]
    
    for text in sample_texts:
        result = predict_sentiment(model, text, vocab_info['word_to_id'], 
                                 vocab_info['label_names'], device=device)
        print(f"\nText: '{text}'")
        print(f"Predicted: {result['predicted_label']} (confidence: {result['confidence']:.3f})")
        print(f"Probabilities: {result['probabilities']}")

def interactive_sentiment_analysis():
    print("=" * 60)
    print("Interactive Sentiment Analysis")
    print("=" * 60)
    
    model_path = 'save/sentiment_models/best_sentiment_model.pt'
    vocab_path = 'save/sentiment_models/vocab.pt'
    
    if not os.path.exists(model_path) or not os.path.exists(vocab_path):
        print("Model or vocabulary files not found. Please train the model first.")
        return
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    vocab_info = torch.load(vocab_path, map_location='cpu')
    
    model = build_sentiment_transformer(
        vocab_size=vocab_info['vocab_size'],
        seq_len=128,
        num_classes=vocab_info['num_classes'],
        d_model=512,
        N=6,
        h=8,
        dropout=0.1,
        d_ff=2048
    ).to(device)
    
    model.load_state_dict(torch.load(model_path, map_location=device))
    
    print("Model loaded successfully!")
    print("Enter text to analyze sentiment (type 'quit' to exit):")
    
    while True:
        text = input("\nEnter text: ").strip()
        
        if text.lower() == 'quit':
            break
        
        if not text:
            print("Please enter some text.")
            continue
        
        result = predict_sentiment(model, text, vocab_info['word_to_id'], 
                                 vocab_info['label_names'], device=device)
        
        print(f"Predicted sentiment: {result['predicted_label']}")
        print(f"Confidence: {result['confidence']:.3f}")
        print("Probability distribution:")
        for label, prob in result['probabilities'].items():
            print(f"  {label}: {prob:.3f}")

def analyze_model_performance():
    print("=" * 60)
    print("Model Performance Analysis")
    print("=" * 60)
    
    results_path = 'save/sentiment_models/evaluation_results.pt'
    if os.path.exists(results_path):
        results = torch.load(results_path, map_location='cpu')
        
        print("Training Results:")
        print(f"Best Validation Accuracy: {results['best_val_accuracy']:.4f}")
        print(f"Final Validation Accuracy: {results['final_val_accuracy']:.4f}")
        print(f"Final Validation Loss: {results['final_val_loss']:.4f}")
        print(f"Training Time: {results['training_time']:.2f} seconds")
        
        if 'classification_report' in results:
            print("\nPer-class Performance:")
            report = results['classification_report']
            for class_name in ['negative', 'neutral', 'positive']:
                if class_name in report:
                    metrics = report[class_name]
                    print(f"{class_name.capitalize()}:")
                    print(f"  Precision: {metrics['precision']:.3f}")
                    print(f"  Recall: {metrics['recall']:.3f}")
                    print(f"  F1-score: {metrics['f1-score']:.3f}")
    else:
        print("Evaluation results not found. Please run training first.")
    
    curves_path = 'save/sentiment_models/training_curves.png'
    if os.path.exists(curves_path):
        print(f"\nTraining curves saved at: {curves_path}")
    
    confusion_path = 'save/sentiment_models/confusion_matrix.png'
    if os.path.exists(confusion_path):
        print(f"Confusion matrix saved at: {confusion_path}")

def main():
    print("Sentiment Analysis Evaluation Menu:")
    print("1. Comprehensive model evaluation")
    print("2. Interactive sentiment analysis")
    print("3. Analyze model performance")
    print("4. Run all evaluations")
    
    choice = input("Enter your choice (1-4): ").strip()
    
    if choice == '1':
        evaluate_model_comprehensive()
    elif choice == '2':
        interactive_sentiment_analysis()
    elif choice == '3':
        analyze_model_performance()
    elif choice == '4':
        evaluate_model_comprehensive()
        print("\n" + "="*60)
        analyze_model_performance()
        print("\n" + "="*60)
        interactive_sentiment_analysis()
    else:
        print("Invalid choice. Please run the script again.")

if __name__ == "__main__":
    main()
