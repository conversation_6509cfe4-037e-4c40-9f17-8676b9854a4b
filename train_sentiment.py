#!/usr/bin/env python
# coding: utf-8

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import os
import time
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import seaborn as sns

from model.sentiment_transformer import build_sentiment_transformer, load_pretrained_encoder
from sentiment_data import prepare_sentiment_data

def get_config(debug=False):
    if debug:
        return {
            'batch_size': 16,
            'max_length': 60,
            'num_epochs': 5,
            'lr': 2e-5,
            'weight_decay': 1e-4,
            'd_model': 256,
            'n_layers': 6,
            'n_heads': 8,
            'd_ff': 1024,
            'dropout': 0.1,
            'pretrained_model_path': 'save/models/model.pt',
            'save_dir': 'save/sentiment_models',
            'max_vocab_size': 5000,
            'min_freq': 2
        }
    else:
        return {
            'batch_size': 32,
            'max_length': 60,
            'num_epochs': 10,
            'lr': 1e-5,
            'weight_decay': 1e-4,
            'd_model': 256,
            'n_layers': 6,
            'n_heads': 8,
            'd_ff': 1024,
            'dropout': 0.1,
            'pretrained_model_path': 'save/models/model.pt',
            'save_dir': 'save/sentiment_models',
            'max_vocab_size': 10000,
            'min_freq': 2
        }

def calculate_accuracy(outputs, labels):
    _, predicted = torch.max(outputs.data, 1)
    total = labels.size(0)
    correct = (predicted == labels).sum().item()
    return correct / total

def evaluate_model(model, test_loader, criterion, device):
    model.eval()
    total_loss = 0
    all_predictions = []
    all_labels = []

    with torch.no_grad():
        for batch in test_loader:
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)

            outputs = model(input_ids, attention_mask)
            loss = criterion(outputs, labels)

            total_loss += loss.item()

            _, predicted = torch.max(outputs.data, 1)
            all_predictions.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    avg_loss = total_loss / len(test_loader)
    accuracy = accuracy_score(all_labels, all_predictions)

    return avg_loss, accuracy, all_predictions, all_labels

def plot_training_curves(train_losses, train_accuracies, val_losses, val_accuracies, save_path):
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

    epochs = range(1, len(train_losses) + 1)

    ax1.plot(epochs, train_losses, 'b-', label='Training Loss')
    ax1.plot(epochs, val_losses, 'r-', label='Validation Loss')
    ax1.set_title('Training and Validation Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)

    ax2.plot(epochs, train_accuracies, 'b-', label='Training Accuracy')
    ax2.plot(epochs, val_accuracies, 'r-', label='Validation Accuracy')
    ax2.set_title('Training and Validation Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def plot_confusion_matrix(y_true, y_pred, class_names, save_path):
    cm = confusion_matrix(y_true, y_pred)

    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def main():
    DEBUG = True
    config = get_config(DEBUG)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    os.makedirs(config['save_dir'], exist_ok=True)

    print("Preparing sentiment data...")
    train_loader, test_loader, vocab_info = prepare_sentiment_data(
        batch_size=config['batch_size'],
        max_length=config['max_length'],
        min_freq=config['min_freq'],
        max_vocab_size=config['max_vocab_size']
    )

    print(f"Vocabulary size: {vocab_info['vocab_size']}")
    print(f"Number of classes: {vocab_info['num_classes']}")
    print(f"Training batches: {len(train_loader)}")
    print(f"Test batches: {len(test_loader)}")

    torch.save(vocab_info, os.path.join(config['save_dir'], 'vocab.pt'))

    print("Building sentiment transformer...")
    model = build_sentiment_transformer(
        vocab_size=vocab_info['vocab_size'],
        seq_len=config['max_length'],
        num_classes=vocab_info['num_classes'],
        d_model=config['d_model'],
        N=config['n_layers'],
        h=config['n_heads'],
        dropout=config['dropout'],
        d_ff=config['d_ff']
    ).to(device)

    if os.path.exists(config['pretrained_model_path']):
        print("Loading pre-trained encoder...")
        model = load_pretrained_encoder(model, config['pretrained_model_path'], device)
    else:
        print("Warning: Pre-trained model not found. Training from scratch.")

    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(model.parameters(), lr=config['lr'], weight_decay=config['weight_decay'])
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=config['num_epochs'])

    train_losses = []
    train_accuracies = []
    val_losses = []
    val_accuracies = []

    best_accuracy = 0.0

    print("Starting training...")
    start_time = time.time()

    for epoch in range(config['num_epochs']):
        model.train()
        epoch_loss = 0
        epoch_accuracy = 0
        num_batches = 0

        batch_iterator = tqdm(train_loader, desc=f'Epoch {epoch+1:02d}/{config["num_epochs"]:02d}')

        for batch in batch_iterator:
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)

            optimizer.zero_grad()
            outputs = model(input_ids, attention_mask)
            loss = criterion(outputs, labels)

            loss.backward()
            optimizer.step()

            accuracy = calculate_accuracy(outputs, labels)

            epoch_loss += loss.item()
            epoch_accuracy += accuracy
            num_batches += 1

            batch_iterator.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{accuracy:.4f}',
                'LR': f'{optimizer.param_groups[0]["lr"]:.2e}'
            })

        scheduler.step()

        avg_train_loss = epoch_loss / num_batches
        avg_train_accuracy = epoch_accuracy / num_batches

        val_loss, val_accuracy, _, _ = evaluate_model(model, test_loader, criterion, device)

        train_losses.append(avg_train_loss)
        train_accuracies.append(avg_train_accuracy)
        val_losses.append(val_loss)
        val_accuracies.append(val_accuracy)

        print(f'Epoch {epoch+1:02d}: Train Loss: {avg_train_loss:.4f}, Train Acc: {avg_train_accuracy:.4f}, '
              f'Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.4f}')

        torch.save(model.state_dict(),
                   os.path.join(config['save_dir'], f'sentiment_model_epoch_{epoch+1}.pt'))

        if val_accuracy > best_accuracy:
            best_accuracy = val_accuracy
            torch.save(model.state_dict(),
                       os.path.join(config['save_dir'], 'best_sentiment_model.pt'))
            print(f'New best model saved with validation accuracy: {best_accuracy:.4f}')

    training_time = time.time() - start_time
    print(f'Training completed in {training_time:.2f} seconds')
    print(f'Best validation accuracy: {best_accuracy:.4f}')

    plot_training_curves(train_losses, train_accuracies, val_losses, val_accuracies,
                         os.path.join(config['save_dir'], 'training_curves.png'))

    print("Evaluating best model...")
    model.load_state_dict(torch.load(os.path.join(config['save_dir'], 'best_sentiment_model.pt')))
    val_loss, val_accuracy, predictions, true_labels = evaluate_model(model, test_loader, criterion, device)

    class_names = list(vocab_info['label_names'].values())
    plot_confusion_matrix(true_labels, predictions, class_names,
                         os.path.join(config['save_dir'], 'confusion_matrix.png'))

    print("\nClassification Report:")
    print(classification_report(true_labels, predictions, target_names=class_names))

    evaluation_results = {
        'final_val_accuracy': val_accuracy,
        'final_val_loss': val_loss,
        'best_val_accuracy': best_accuracy,
        'training_time': training_time,
        'classification_report': classification_report(true_labels, predictions,
                                                      target_names=class_names, output_dict=True)
    }

    torch.save(evaluation_results, os.path.join(config['save_dir'], 'evaluation_results.pt'))

    print(f"All results saved to {config['save_dir']}")

if __name__ == "__main__":
    main()
